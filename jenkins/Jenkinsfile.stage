def COLOR_MAP = [
    'FAILURE' : 'danger',
    'SUCCESS' : 'good'
]
pipeline {
    agent {
        label 'piximind-dev-node'
    }

    tools { 
        nodejs 'nodejs_20' 
    }


    environment {
        TAG = "${BUILD_NUMBER}"
        IMAGE_NAME = "generator_backend_stage:${TAG}"
        GITLAB_CREDENTIALS = credentials('gitlab-credentials')
        env_file = credentials('generator-backend-env-stage')
        env_file_db = credentials('generator-database-env-stage')
        DOCKER_COMPOSE_FILE = './docker-compose/stage.yml'
    }

    stages {

         stage('Check SCM from Gitlab') {
            steps {
                script {
                    CI_ERROR = "Failed while checking out SCM"
                    try {
                        echo 'Checking out SCM...'
                        checkout([$class: 'GitSCM', 
                            branches: [[name: '*/dev']],
                            doGenerateSubmoduleConfigurations: false,
                            extensions: [], 
                            userRemoteConfigs: [[
                                credentialsId: 'gitlab-credentials', 
                                url: 'https://gitlab.docker.piximind.com/pfa_2025/pixigenerator_be.git'
                            ]]
                        ])
                    } catch (Exception e) {
                        error(CI_ERROR)
                    }
                }
            }
        }

        stage('Display Review') {
            steps {
                script {
                    try {
                        currentBuild.displayName = "Deploy on ${GIT_BRANCH} [#${BUILD_NUMBER}]"
                        currentBuild.description = "Committer: ${gitlabUserName}\n" +
                                                "Commit: ${gitlabMergeRequestLastCommit}"
                    } catch (err) {
                        echo 'The build is Triggered Manually'
                    }
                }
            }
        }


        stage('Build Backend Image') {
            steps {
                script {
                    CI_ERROR = "Failed while building the application"
                    try {
                        echo 'Building Docker Image ...'
                        def dockerHome = tool 'docker'
                        env.PATH = "${dockerHome}/bin:${env.PATH}"
                        sh "docker build --tag ${IMAGE_NAME} -f Dockerfile.stage ."
                        sh 'echo Building project...'
                    } catch (Exception e) {
                        error(CI_ERROR)
                    }
                }
            }
        }

        stage('Security Scan: Trivy') {
            steps {
                script {
                    CI_ERROR = "Failed during security scan for the application"
                    try {
                        echo 'Running security scan on the application...'
                        // Add your Trivy scan steps here
                    } catch (Exception e) {
                        error(CI_ERROR)
                    }
                }
            }
        }

        stage('Deploy for QA Tester') {
            environment {
                env_file = credentials('generator-backend-env-stage')
                env_file_db = credentials('generator-database-env-stage')
            }
            steps {
                script {
                    CI_ERROR = "Failed while deploying the application"
                    try {
                        echo 'Deploying application...'
                        sh 'export TAG=${TAG} && echo $TAG'
                        sh """
                        docker-compose \
                        -f ${DOCKER_COMPOSE_FILE} \
                        up -d
                        """
                        sh 'echo Deploying project...'
                    } catch (Exception e) {
                        error(CI_ERROR)
                    }
                }
            }
        }


    }



    post {
        always {
            script {
                BUILD_STATUS = currentBuild.currentResult
                if (BUILD_STATUS == 'SUCCESS') {
                    CI_ERROR = "NA"
                }

                // Declare and assign emojis within the script block
                def emoji = BUILD_STATUS == 'SUCCESS' ? ":white_check_mark:" : ":x:"
                def statusEmoji = BUILD_STATUS == 'SUCCESS' ? ":tada:" : ":exclamation:"

                // Get the build user and branch name
                def buildUser = currentBuild.getBuildCauses()?.find { it._class.contains('UserIdCause') }?.userId ?: "Unknown"
                def buildBranch = env.GIT_BRANCH ?: "Unknown"

                echo 'Sending Slack Notification'
                slackSend (
                    channel: '#jenkins',
                    iconEmoji: emoji,
                    color: COLOR_MAP[BUILD_STATUS],
                    message: "*${BUILD_STATUS}:* Job *${env.JOB_NAME}* ${statusEmoji} \n *Build Number: * *${env.BUILD_NUMBER}* \n *CI Error:* ${CI_ERROR} \n *Build User:* ${buildUser} \n *Build Branch:* ${buildBranch} \n *More info at: * ${env.BUILD_URL}"
                )
            }
        }
    }


}
