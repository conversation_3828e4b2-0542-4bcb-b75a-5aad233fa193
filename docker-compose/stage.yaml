version: '3.7'
services:

  generator-stage:
    container_name: generator-backend-stage
    ports:
      - "${GENERATOR_BACKEND_STAGE_PORT:-7003}:${BACKEND_PORT:-3000}"
    image: ${IMAGE_NAME}
    env_file:
      - ${env_file:-.env}
    restart: unless-stopped
    depends_on:
      - postgres-generator-stage
    networks:
      - generator-network

  postgres-generator-stage:
    image: postgres:16.1-alpine
    container_name: generator-postgres-stage
    restart: unless-stopped
    ports:
      - "${GENERATOR_DATABASE_STAGE_PORT:-5436}:${DATABASE_PORT:-5432}"
    volumes:
      - postgres-generator-stage:/data/postgres
    env_file:
      - ${env_file_db:-.env.db}
    networks:
      - generator-network
  
  generator-adminer-stage:
    image: adminer:latest
    container_name: generator-adminer-stage
    restart: unless-stopped
    ports:
      - "8397:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=postgres-generator-stage  
      - ADMINER_DESIGN=flat
    networks:
      - generator-network
    depends_on:
      - postgres-generator-stage
  

volumes:
  postgres-generator-stage:

networks:
  generator-network:
    external: true
