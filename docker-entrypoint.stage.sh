#!/bin/sh
set -e

cat << "EOF"
### ██████╗  ██╗ ██╗  ██╗ ██╗ ███╗   ███╗ ██╗ ███╗   ██╗ ██████╗    
### ██╔══██╗ ██║ ╚██╗██╔╝ ██║ ████╗ ████║ ██║ ████╗  ██║ ██╔══██╗ 
### ██████╔╝ ██║  ╚███╔╝  ██║ ██╔████╔██║ ██║ ██╔██╗ ██║ ██║  ██║ 
### ██╔═══╝  ██║  ██╔██╗  ██║ ██║╚██╔╝██║ ██║ ██║╚██╗██║ ██║  ██║ 
### ██║      ██║ ██╔╝ ██╗ ██║ ██║ ╚═╝ ██║ ██║ ██║ ╚████║ ██████╔╝ 
### ╚═╝      ╚═╝ ╚═╝  ╚═╝ ╚═╝ ╚═╝     ╚═╝ ╚═╝ ╚═╝  ╚═══╝ ╚═════╝  
EOF

echo "🌿 RUNNING DOCKER ENTRYPOINT"
echo "⏰ Current time: $(date)"

# Validate DATABASE_URL
if [ -z "$DATABASE_URL" ]; then
  echo "🍂 ERROR: DATABASE_URL is not set. Exiting..."
  exit 1
fi


# Run database migrations
echo "🌍 Running database migrations..."
npm run prisma:migrate
echo "🍃 Migration done: $(date)"

# Generate Prisma client
echo "🌻 Generating Prisma client..."
npm run prisma:generate
echo "🍃 Prisma client generated!"


# Run seed
echo "🌾 Running database seed..."
npm run seed
echo "🍃 Database seeded!"

# Start application
echo "🌞 Starting application PRODUCTION mode..."
node ./dist/main
