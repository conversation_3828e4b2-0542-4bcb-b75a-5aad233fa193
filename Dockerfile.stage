FROM node:22.11.0 AS base

RUN apt-get update && apt-get install -y \
  libx11-xcb1 \
  libatk1.0-0 \
  libatk-bridge2.0-0 \
  libcups2 \
  libdrm2 \
  libxkbcommon0 \
  libxcomposite1 \
  libxdamage1 \
  libxrandr2 \
  libgbm1 \
  libasound2 \
  libpangocairo-1.0-0 \
  libxshmfence1 \
  fonts-liberation \
  xdg-utils \
  libnss3 \
  lsb-release \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY package.json ./
COPY package-lock.json ./
RUN npm install -f

COPY prisma/schema.prisma ./prisma/

RUN npm run prisma:generate

COPY . .

RUN npm run build

EXPOSE 3000

RUN sed -i 's/\r//' ./docker-entrypoint.stage.sh \
    && chmod +x ./docker-entrypoint.stage.sh

ENTRYPOINT ["./docker-entrypoint.stage.sh"]
