import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserRole } from 'generated/prisma';
import { Roles } from '../decorators/roles.decorator';
import { AuthenticatedRequest } from '../interfaces/authenticated-request.interface';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const roles = this.reflector.get(Roles, context.getHandler());
    if (!roles) {
      return true;
    }
    const request: AuthenticatedRequest = context.switchToHttp().getRequest();
    const user = request.user;
    return this.matchRoles(roles, user?.role as UserRole);
  }

  private matchRoles(
    roles: UserRole[],
    userRole: UserRole | undefined,
  ): boolean {
    if (!userRole) return false;
    return roles.includes(userRole);
  }
}
