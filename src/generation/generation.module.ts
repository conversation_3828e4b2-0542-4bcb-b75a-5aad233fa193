import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { GenerationService } from './generation.service';
import {
  GenerationController,
  ProjectGenerationController,
} from './generation.controller';
import { PrismaService } from '../prisma.service';
import { AssistantModule } from '../assistant/assistant.module';

@Module({
  imports: [
    AssistantModule,
    ConfigModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'default-secret',
      signOptions: { expiresIn: '1h' },
    }),
  ],
  controllers: [GenerationController, ProjectGenerationController],
  providers: [GenerationService, PrismaService],
  exports: [GenerationService],
})
export class GenerationModule {}
