import {
  Injectable,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HfInference } from '@huggingface/inference';
import { GenerationType } from 'generated/prisma';
import {
  AssistantRequest,
  AssistantResponse,
  HuggingFaceConfig,
  AssistantMessage,
} from './interfaces/assistant.interface';

@Injectable()
export class AssistantService {
  private readonly logger = new Logger(AssistantService.name);
  private readonly hf: HfInference;
  private readonly config: HuggingFaceConfig;

  constructor(private configService: ConfigService) {
    this.config = {
      apiKey: this.configService.get<string>('HUGGINGFACE_API_KEY') || '',
      baseUrl: this.configService.get<string>('HUGGINGFACE_BASE_URL'),
      defaultModel:
        this.configService.get<string>('HUGGINGFACE_DEFAULT_MODEL') ||
        'microsoft/DialoGPT-medium',
      uiModel:
        this.configService.get<string>('HUGGINGFACE_UI_MODEL') ||
        'Salesforce/codegen-350M-mono',
      documentationModel:
        this.configService.get<string>('HUGGINGFACE_DOCS_MODEL') ||
        'microsoft/DialoGPT-medium',
      maxTokens: parseInt(
        this.configService.get<string>('HUGGINGFACE_MAX_TOKENS') || '1000',
      ),
      temperature: parseFloat(
        this.configService.get<string>('HUGGINGFACE_TEMPERATURE') || '0.7',
      ),
    };

    if (!this.config.apiKey) {
      this.logger.warn(
        'HUGGINGFACE_API_KEY not found. Assistant service will use public inference API with rate limits.',
      );
    }

    this.hf = new HfInference(this.config.apiKey);
  }

  async processRequest(request: AssistantRequest): Promise<AssistantResponse> {
    const startTime = Date.now();

    try {
      this.logger.log(
        `Processing ${request.generationType} generation request`,
      );

      // Select appropriate model based on generation type
      const model = this.getModelForType(request.generationType);

      // Build conversation context
      const conversationContext = this.buildConversationContext(request);

      // Generate response based on type
      let response: AssistantResponse;

      if (request.generationType === GenerationType.UI) {
        response = await this.generateUICode(
          conversationContext,
          model,
          startTime,
        );
      } else {
        response = await this.generateDocumentation(
          conversationContext,
          model,
          startTime,
        );
      }

      this.logger.log(
        `Generated ${request.generationType} response in ${response.processingTime}ms`,
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Assistant processing failed: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Failed to process assistant request',
      );
    }
  }

  private getModelForType(type: GenerationType): string {
    switch (type) {
      case GenerationType.UI:
        return this.config.uiModel!;
      case GenerationType.DOCUMENTATION:
        return this.config.documentationModel!;
      default:
        return this.config.defaultModel!;
    }
  }

  private buildConversationContext(request: AssistantRequest): string {
    let context = '';

    // Add system context based on generation type
    if (request.generationType === GenerationType.UI) {
      context +=
        'You are an expert React/TypeScript developer specializing in creating modern, responsive UI components with Tailwind CSS. ';
      context +=
        'Generate clean, production-ready code with proper TypeScript types and accessibility features.\n\n';
    } else {
      context +=
        'You are a technical documentation expert. Create clear, comprehensive documentation with examples and best practices.\n\n';
    }

    // Add project context if available
    if (request.metadata?.projectContext) {
      context += `Project Context: ${request.metadata.projectContext}\n\n`;
    }

    // Add conversation history
    if (request.conversationHistory.length > 0) {
      context += 'Previous conversation:\n';
      request.conversationHistory.forEach((message, index) => {
        if (message.role === 'USER') {
          context += `User: ${message.content}\n`;
        } else if (message.role === 'ASSISTANT') {
          context += `Assistant: ${message.content}\n`;
        }
      });
      context += '\n';
    }

    // Add current user message
    context += `Current request: ${request.userMessage}`;

    return context;
  }

  private async generateUICode(
    context: string,
    model: string,
    startTime: number,
  ): Promise<AssistantResponse> {
    try {
      // For UI generation, we'll use text generation with specific prompts
      const prompt = `${context}\n\nGenerate a React TypeScript component with Tailwind CSS. Include:\n1. Proper TypeScript interfaces\n2. Responsive design\n3. Accessibility features\n4. Clean, modern styling\n\nComponent code:`;

      const result = await this.hf.textGeneration({
        model,
        inputs: prompt,
        parameters: {
          max_new_tokens: this.config.maxTokens,
          temperature: this.config.temperature,
          return_full_text: false,
        },
      });

      const generatedCode = result.generated_text || '';

      return {
        content: "Here's your React component:",
        outputData: {
          code: generatedCode,
          preview: this.generatePreviewHtml(generatedCode),
          metadata: {
            framework: 'React',
            styling: 'Tailwind CSS',
            language: 'TypeScript',
          },
        },
        processingTime: Date.now() - startTime,
        model,
      };
    } catch (error) {
      this.logger.error(`UI generation failed: ${error.message}`);
      throw error;
    }
  }

  private async generateDocumentation(
    context: string,
    model: string,
    startTime: number,
  ): Promise<AssistantResponse> {
    try {
      const prompt = `${context}\n\nGenerate comprehensive technical documentation. Include:\n1. Clear explanations\n2. Code examples\n3. Best practices\n4. Common pitfalls\n\nDocumentation:`;

      const result = await this.hf.textGeneration({
        model,
        inputs: prompt,
        parameters: {
          max_new_tokens: this.config.maxTokens,
          temperature: this.config.temperature,
          return_full_text: false,
        },
      });

      const generatedDocs = result.generated_text || '';

      return {
        content: generatedDocs,
        outputData: {
          documentation: generatedDocs,
          metadata: {
            type: 'technical_documentation',
            format: 'markdown',
          },
        },
        processingTime: Date.now() - startTime,
        model,
      };
    } catch (error) {
      this.logger.error(`Documentation generation failed: ${error.message}`);
      throw error;
    }
  }

  private generatePreviewHtml(componentCode: string): string {
    // Simple HTML preview generator for React components
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Component Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="p-8 bg-gray-100">
    <div id="preview">
        <!-- Component preview would be rendered here -->
        <div class="bg-white p-4 rounded-lg shadow">
            <h3 class="text-lg font-semibold mb-2">Generated Component Preview</h3>
            <p class="text-gray-600">Component code generated successfully. Use a React environment to see the live preview.</p>
        </div>
    </div>
</body>
</html>`;
  }

  async healthCheck(): Promise<{ status: string; models: string[] }> {
    try {
      return {
        status: 'healthy',
        models: [
          this.config.defaultModel!,
          this.config.uiModel!,
          this.config.documentationModel!,
        ],
      };
    } catch (error) {
      this.logger.error(`Health check failed: ${error.message}`);
      return {
        status: 'unhealthy',
        models: [],
      };
    }
  }
}
