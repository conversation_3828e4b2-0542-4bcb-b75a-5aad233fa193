import { GenerationType, MessageRole } from 'generated/prisma';

export interface AssistantMessage {
  role: MessageRole;
  content: string;
  inputData?: any;
  outputData?: any;
}

export interface AssistantRequest {
  generationType: GenerationType;
  conversationHistory: AssistantMessage[];
  userMessage: string;
  inputData?: any;
  metadata?: {
    projectContext?: string;
    previousGenerations?: string[];
    userPreferences?: any;
  };
}

export interface AssistantResponse {
  content: string;
  outputData?: {
    code?: string;
    preview?: string;
    documentation?: string;
    metadata?: any;
  };
  processingTime: number;
  model?: string;
  tokensUsed?: number;
}

export interface HuggingFaceConfig {
  apiKey: string;
  baseUrl?: string;
  defaultModel?: string;
  uiModel?: string;
  documentationModel?: string;
  maxTokens?: number;
  temperature?: number;
}

export interface ModelResponse {
  generated_text?: string;
  error?: string;
  estimated_time?: number;
}
