import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { ConfigModule } from '@nestjs/config';
import { UsersModule } from './users/users.module';
import { APP_GUARD } from '@nestjs/core';
import { AuthGuard } from './auth/guards/auth.guard';
import { RolesGuard } from './auth/guards/roles.guard';
import { MailerModule } from './mailer/mailer.module';
import { InvitationModule } from './invitation/invitation.module';
import { TokenModule } from './token/token.module';
import { ProjectModule } from './project/project.module';
import { PromptModule } from './prompt/prompt.module';
import { GenerationModule } from './generation/generation.module';
@Module({
  imports: [
    AuthModule,
    ConfigModule.forRoot(),
    UsersModule,
    MailerModule.forRoot({
      host: process.env.EMAIL_HOST || 'localhost',
      port: process.env.EMAIL_PORT ? parseInt(process.env.EMAIL_PORT, 10) : 587,
      secure: false,
      auth: {
        user: process.env.EMAIL_USERNAME || 'admin',
        pass: process.env.EMAIL_PASSWORD || 'admin',
      },
    }),
    InvitationModule,
    TokenModule,
    ProjectModule,
    PromptModule,
    GenerationModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: AuthGuard, // Tous les routes seront protégées par défaut
    },
    {
      provide: APP_GUARD,
      useClass: RolesGuard, // Vérification des rôles pour les routes protégées
    },
  ],
})
export class AppModule {}
