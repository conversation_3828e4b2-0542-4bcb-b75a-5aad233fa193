services:
  generator-backend:
    container_name: generator-backend-local
    ports:
      - '${GENERATOR_BACKEND_LOCAL_PORT:-3007}:${BACKEND_PORT:-3000}'
    build:
      context: .
      dockerfile: Dockerfile.local
    env_file:
      - .env.local
    volumes:
      - ./src:/app/src
    restart: unless-stopped
    depends_on:
      - generator-database
    networks:
      - generator-network

  generator-database:
    image: postgres:16.1-alpine
    container_name: generator-postgres-local
    restart: unless-stopped
    ports:
      - '5435:5432'
      - '${GENERATOR_DATABASE_LOCAL_PORT:-5432}:${DATABASE_PORT:-5432}'
    volumes:
      - postgres-generator:/data/postgres
    env_file:
      - .env.db
    healthcheck:
      test:
        ['CMD', 'pg_isready', '-U', 'generator-local', '-P', 'bvskj463m97HKD88']
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - generator-network

  pgadmin:
    image: dpage/pgadmin4
    container_name: generator-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: GenERJFNG34$$*
    ports:
      - '5050:80'
    volumes:
      - pgadmin-data-generator:/var/lib/pgadmin
    networks:
      - generator-network

volumes:
  postgres-generator:
  pgadmin-data-generator:

networks:
  generator-network:
    external: true
